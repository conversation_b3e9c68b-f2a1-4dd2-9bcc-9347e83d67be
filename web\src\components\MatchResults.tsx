import styled from "@emotion/styled";
import { Button, Flex, Text, Table, Progress, Badge, Group, Card } from "@mantine/core";
import React, { FC } from "react";
import { useGlobalStore, MatchResultsData, PlayerStats } from "../states/useGlobalState";
import { fetchNui } from "../utils/fetchNui";

const ResultsContainer = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
`

const ResultsPanel = styled.div`
    background: var(--background-color);
    border-radius: 15px;
    padding: 30px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid var(--highlight-color);
`



interface MatchResultsProps {
    results: MatchResultsData;
    onClose: () => void;
}

export const MatchResults: FC<MatchResultsProps> = ({ results, onClose }) => {
    const messages = useGlobalStore(state => state.messages);

    const getWinnerText = () => {
        if (results.winner === 0) return "DRAW";
        return `TEAM ${results.winner} WINS`;
    };

    const getWinnerColor = () => {
        if (results.winner === 0) return "yellow";
        return results.winner === 1 ? "blue" : "red";
    };

    const handleClose = async () => {
        await fetchNui('closeMatchResults', {}, true);
        onClose();
    };

    const renderPlayerTable = (players: PlayerStats[], teamName: string, teamColor: string) => (
        <Card shadow="sm" padding="lg" radius="md" withBorder style={{ marginBottom: '20px' }}>
            <Text size="xl" weight={700} color={teamColor} align="center" mb="md">
                {teamName}
            </Text>
            <Table striped highlightOnHover>
                <thead>
                    <tr>
                        <th>Player</th>
                        <th>K/D</th>
                        <th>KDR</th>
                        <th>Accuracy</th>
                        <th>Damage</th>
                        <th>Rounds Won</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {players.map((player) => (
                        <tr key={player.id} style={{ backgroundColor: player.mvp ? 'rgba(255, 215, 0, 0.1)' : 'transparent' }}>
                            <td>
                                <Group spacing="xs">
                                    <Text weight={player.mvp ? 700 : 400}>{player.name}</Text>
                                    {player.mvp && <Badge color="yellow" size="xs">MVP</Badge>}
                                </Group>
                            </td>
                            <td>{player.kills}/{player.deaths}</td>
                            <td>{player.kd.toFixed(2)}</td>
                            <td>
                                <Group spacing="xs">
                                    <Progress value={player.accuracy} size="sm" style={{ width: '60px' }} />
                                    <Text size="sm">{player.accuracy.toFixed(1)}%</Text>
                                </Group>
                            </td>
                            <td>{player.damageDealt}</td>
                            <td>{player.roundsWon}</td>
                            <td>
                                {player.mvp && <Badge color="gold">MVP</Badge>}
                                {player.kills === Math.max(...[...results.team1, ...results.team2].map(p => p.kills)) && !player.mvp && (
                                    <Badge color="orange">Top Fragger</Badge>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>
        </Card>
    );

    return (
        <ResultsContainer>
            <ResultsPanel>
                <Flex direction="column" align="center" gap="md">
                    {/* Match Result Header */}
                    <Text size={48} weight={900} color={getWinnerColor()} align="center">
                        {getWinnerText()}
                    </Text>
                    
                    <Text size="xl" color="dimmed" align="center">
                        Final Score: {results.finalScore.team1} - {results.finalScore.team2}
                    </Text>

                    {/* Match Info */}
                    <Card shadow="sm" padding="lg" radius="md" withBorder style={{ width: '100%', marginBottom: '20px' }}>
                        <Text size="lg" weight={700} mb="md" align="center">Match Summary</Text>
                        <Flex justify="space-around" wrap="wrap">
                            <div>
                                <Text size="sm" color="dimmed">Match ID</Text>
                                <Text weight={600}>#{results.matchId}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Map</Text>
                                <Text weight={600}>{results.map}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Weapon</Text>
                                <Text weight={600}>{results.weapon}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Duration</Text>
                                <Text weight={600}>{results.duration}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Total Kills</Text>
                                <Text weight={600}>{results.matchStats.totalKills}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Rounds Played</Text>
                                <Text weight={600}>{results.matchStats.totalRounds}</Text>
                            </div>
                        </Flex>
                    </Card>

                    {/* Team Statistics */}
                    <Flex direction="row" gap="lg" style={{ width: '100%' }} wrap="wrap">
                        <div style={{ flex: 1, minWidth: '400px' }}>
                            {renderPlayerTable(results.team1, "Team 1", "blue")}
                        </div>
                        <div style={{ flex: 1, minWidth: '400px' }}>
                            {renderPlayerTable(results.team2, "Team 2", "red")}
                        </div>
                    </Flex>

                    {/* Additional Match Stats */}
                    <Card shadow="sm" padding="lg" radius="md" withBorder style={{ width: '100%' }}>
                        <Text size="lg" weight={700} mb="md" align="center">Match Statistics</Text>
                        <Flex justify="space-around" wrap="wrap">
                            <div>
                                <Text size="sm" color="dimmed">Longest Round</Text>
                                <Text weight={600}>{results.matchStats.longestRound}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">Shortest Round</Text>
                                <Text weight={600}>{results.matchStats.shortestRound}</Text>
                            </div>
                            <div>
                                <Text size="sm" color="dimmed">End Reason</Text>
                                <Text weight={600}>{results.reason.replace('_', ' ').toUpperCase()}</Text>
                            </div>
                        </Flex>
                    </Card>

                    {/* Close Button */}
                    <Button
                        size="lg"
                        onClick={handleClose}
                        style={{
                            backgroundColor: 'var(--highlight-color)',
                            marginTop: '20px'
                        }}
                    >
                        Close Results
                    </Button>
                </Flex>
            </ResultsPanel>
        </ResultsContainer>
    );
};
