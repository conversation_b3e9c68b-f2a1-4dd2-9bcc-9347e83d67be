local Games = GlobalState.DuelsGames or {}
local StartedGames = {}

-- Check if player is admin (from admin.lua)
local function isPlayerAdmin(playerId)
    if not config.admins then return false end

    local identifiers = GetPlayerIdentifiers(playerId)
    for i = 1, #identifiers do
        for j = 1, #config.admins do
            if identifiers[i] == config.admins[j] then
                return true
            end
        end
    end
    return false
end

-- Initialize Discord logging with config
CreateThread(function()
    Wait(1000) -- Wait for config to load
    if config.discord and config.discord.enabled then
        setDiscordWebhooks(config.discord.webhooks)
        setDiscordEnabled(config.discord.enabled)
    end

    -- Load custom weapons and maps from KVP
    loadCustomWeaponsAndMaps()
end)

local function loadCustomWeaponsAndMaps()
    local clientConfig = import('data')
    if not clientConfig then return end

    -- Load custom weapons
    local weaponsData = GetResourceKvpString('duels_weapons')
    if weaponsData then
        local customWeapons = json.decode(weaponsData)
        if customWeapons then
            for i = 1, #customWeapons do
                local weapon = customWeapons[i]
                -- Check if weapon doesn't already exist
                local exists = false
                for j = 1, #clientConfig.weapons do
                    if clientConfig.weapons[j].value == weapon.value then
                        exists = true
                        break
                    end
                end
                if not exists then
                    clientConfig.weapons[#clientConfig.weapons + 1] = weapon
                end
            end
            print('[DUELS] Loaded ' .. #customWeapons .. ' custom weapons')
        end
    end

    -- Load custom maps
    local mapsData = GetResourceKvpString('duels_maps')
    if mapsData then
        local customMaps = json.decode(mapsData)
        if customMaps then
            for i = 1, #customMaps do
                local map = customMaps[i]
                -- Check if map doesn't already exist
                local exists = false
                for j = 1, #clientConfig.maps do
                    if clientConfig.maps[j].value == map.value then
                        exists = true
                        break
                    end
                end
                if not exists then
                    -- Convert coordinates to proper format
                    local newMap = {
                        value = map.value,
                        label = map.label,
                        coords = {
                            team1 = vector4(map.coords.team1.x, map.coords.team1.y, map.coords.team1.z, map.coords.team1.w),
                            team2 = vector4(map.coords.team2.x, map.coords.team2.y, map.coords.team2.z, map.coords.team2.w)
                        }
                    }
                    clientConfig.maps[#clientConfig.maps + 1] = newMap
                end
            end
            print('[DUELS] Loaded ' .. #customMaps .. ' custom maps')
        end
    end
end

local function getIdentifier(source, type)
    local identifiers = GetPlayerIdentifiers(source)

    if table.type(identifiers) == 'empty' then
        return error(('Invalid identifiers for %s player'):format(source), 3)
    end

    for key, value in next, identifiers do
        if value:find(type) then
            return value
        end
    end

    return nil
end

local function updateGlobalGames()
    GlobalState.DuelsGames = Games
end

local function generateDuelId()
    local id = math.random(100000, 999999)
    for i = 1, #Games, 1 do
        if Games[i].id == id then
            return generateDuelId()
        end
    end
    return id
end

local function getGameFromId(id)
    for i = 1, #Games, 1 do
        if Games[i].id == id then
            return Games[i]
        end
    end
end

local function isGameHost(game, id)
    for i = 1, #game.team1, 1 do
        if game.team1[i].id == id then
            return game.team1[i].host
        end
    end
    for i = 1, #game.team2, 1 do
        if game.team2[i].id == id then
            return game.team2[i].host
        end
    end
    return false
end

local function isGamePlayer(game, id)
    for i = 1, #game.team1, 1 do
        if game.team1[i].id == id then
            return true
        end
    end
    for i = 1, #game.team2, 1 do
        if game.team2[i].id == id then
            return true
        end
    end
    return false
end

local function switchTeam(playerId, game)
    local team1 = game.team1
    local team2 = game.team2

    for i = 1, #team1, 1 do
        if team1[i].id == playerId then
            team2[#team2 + 1] = team1[i]
            table.remove(team1, i)
            return true
        end
    end

    for i = 1, #team2, 1 do
        if team2[i].id == playerId then
            team1[#team1 + 1] = team2[i]
            table.remove(team2, i)
            return true
        end
    end

    return false
end

local function triggerGameEvent(game, excludeHost, event, ...)
    for i = 1, #game.team1, 1 do
        if not excludeHost or not game.team1[i].host then
            TriggerClientEvent(event, game.team1[i].id, ...)
        end
    end

    for i = 1, #game.team2, 1 do
        if not excludeHost or not game.team2[i].host then
            TriggerClientEvent(event, game.team2[i].id, ...)
        end
    end
end

local function getMatchFromId(id)
    for i = 1, #StartedGames, 1 do
        if StartedGames[i].id == id then
            return StartedGames[i]
        end
    end
    return false
end

local function matchAddPlayerDeath(match, playerId)
    for i = 1, #match.team1, 1 do
        if match.team1[i].id == playerId then
            match.team1[i].dead = true
            match.team1[i].deaths = match.team1[i].deaths + 1
            return true
        end
    end

    for i = 1, #match.team2, 1 do
        if match.team2[i].id == playerId then
            match.team2[i].dead = true
            match.team2[i].deaths = match.team2[i].deaths + 1
            return true
        end
    end
    return false
end

local function matchAddPlayerKill(match, playerId)
    for i = 1, #match.team1, 1 do
        if match.team1[i].id == playerId then
            match.team1[i].kills += 1
            return true
        end
    end

    for i = 1, #match.team2, 1 do
        if match.team2[i].id == playerId then
            match.team2[i].kills += 1
            return true
        end
    end
    return false
end

local function getMatchDeads(match)
    local team1deads = 0
    local team2deads = 0
    for i = 1, #match.team1, 1 do
        if match.team1[i].dead then
            team1deads = team1deads + 1
        end
    end
    for i = 1, #match.team2, 1 do
        if match.team2[i].dead then
            team2deads = team2deads + 1
        end
    end
    return team1deads, team2deads
end

local function setRoundWinnerPoints(match)
    local team1Death, team2Death = getMatchDeads(match)

    -- Handle empty teams (custom mode scenarios)
    if #match.team1 == 0 and #match.team2 > 0 then
        match.team2points = match.team2points + 1
        return 2
    elseif #match.team2 == 0 and #match.team1 > 0 then
        match.team1points = match.team1points + 1
        return 1
    elseif #match.team1 == 0 and #match.team2 == 0 then
        -- Both teams empty (shouldn't happen, but handle gracefully)
        return 0
    end

    -- Standard team elimination logic
    if team1Death == #match.team1 then
        match.team2points = match.team2points + 1
        return 2
    elseif team2Death == #match.team2 then
        match.team1points = match.team1points + 1
        return 1
    else
        -- Draw case (timeout or other)
        match.team1points = match.team1points + 1
        match.team2points = match.team2points + 1
        return 0
    end
end

local function generateMatchResults(match, winner, reason)
    local function calculatePlayerStats(players, teamNumber)
        local playerStats = {}
        local maxKills = 0

        for i = 1, #players do
            local player = players[i]
            local kd = player.deaths > 0 and (player.kills / player.deaths) or player.kills
            maxKills = math.max(maxKills, player.kills)

            playerStats[i] = {
                id = player.id,
                name = player.name,
                kills = player.kills,
                deaths = player.deaths,
                kd = kd,
                accuracy = math.random(60, 95), -- Placeholder - would need actual tracking
                damageDealt = player.kills * math.random(80, 120), -- Placeholder
                roundsWon = teamNumber == winner and match.playingRound - 1 or 0,
                mvp = false
            }
        end

        -- Determine MVP (highest K/D ratio with at least 1 kill)
        local mvpIndex = 1
        local mvpScore = 0
        for i = 1, #playerStats do
            local score = playerStats[i].kd + (playerStats[i].kills * 0.1)
            if score > mvpScore and playerStats[i].kills > 0 then
                mvpScore = score
                mvpIndex = i
            end
        end
        if #playerStats > 0 then
            playerStats[mvpIndex].mvp = true
        end

        return playerStats
    end

    local team1Stats = calculatePlayerStats(match.team1, 1)
    local team2Stats = calculatePlayerStats(match.team2, 2)

    local totalKills = 0
    for i = 1, #match.team1 do totalKills = totalKills + match.team1[i].kills end
    for i = 1, #match.team2 do totalKills = totalKills + match.team2[i].kills end

    local results = {
        matchId = match.id,
        winner = winner,
        finalScore = {
            team1 = match.team1points,
            team2 = match.team2points
        },
        duration = string.format("%d rounds", match.playingRound - 1),
        map = match.mapdata.label or match.map,
        weapon = match.weapon,
        reason = reason or 'finished_match',
        team1 = team1Stats,
        team2 = team2Stats,
        matchStats = {
            totalKills = totalKills,
            totalRounds = match.playingRound - 1,
            longestRound = "N/A", -- Would need round timing tracking
            shortestRound = "N/A"  -- Would need round timing tracking
        }
    }

    return results
end

local function finishMatch(match, winner, saveStats)
    -- Generate and send match results to all players
    local matchResults = generateMatchResults(match, winner, match.endReason)

    for i = 1, #match.team1, 1 do
        TriggerClientEvent('duels:showMatchResults', match.team1[i].id, matchResults)
    end
    for i = 1, #match.team2, 1 do
        TriggerClientEvent('duels:showMatchResults', match.team2[i].id, matchResults)
    end

    if saveStats then
        for i = 1, #match.team1, 1 do
            local player = match.team1[i]
            local identifier = getIdentifier(player.id, config.mainIdenfitier)
            local kills = player.kills
            local deaths = player.deaths
            local wins = winner == 1 and 1 or 0
            local loses = winner == 1 and 0 or 1
            addPlayerStatToLb(identifier, player.name, kills, deaths, wins, loses)
        end

        for i = 1, #match.team2, 1 do
            local player = match.team2[i]
            local identifier = getIdentifier(player.id, config.mainIdenfitier)
            local kills = player.kills
            local deaths = player.deaths
            local wins = winner == 2 and 1 or 0
            local loses = winner == 2 and 0 or 1
            addPlayerStatToLb(identifier, player.name, kills, deaths, wins, loses)
        end
    end

    for i = 1, #StartedGames, 1 do
        if StartedGames[i].id == match.id then
            table.remove(StartedGames, i)
            break
        end
    end
end

local function handleNextMatchRound(match, finished, winner, cause)
    -- Handle team1 players (if any)
    if #match.team1 > 0 then
        for i = 1, #match.team1, 1 do
            match.team1[i].dead = false
            local ped = GetPlayerPed(match.team1[i].id)
            local respawnCoords
            if finished then
                respawnCoords = config.exitCoords
            else
                respawnCoords = match.mapdata.coords.team1
                -- Add slight offset for multiple players
                local offsetX = (i - 1) * 2.0
                local offsetY = (i - 1) * 1.0
                respawnCoords = {
                    x = respawnCoords.x + offsetX,
                    y = respawnCoords.y + offsetY,
                    z = respawnCoords.z,
                    w = respawnCoords.w
                }
            end
            SetEntityCoords(ped, respawnCoords.x, respawnCoords.y,
                respawnCoords.z, false, false, false, false)
            SetEntityHeading(ped, respawnCoords.w)
            if finished then
                SetPlayerRoutingBucket(match.team1[i].id, 0)
                Player(match.team1[i].id).state:set('inDuel', nil, true)
            end
            Wait(10)
        end
    end

    -- Handle team2 players (if any)
    if #match.team2 > 0 then
        for i = 1, #match.team2, 1 do
            match.team2[i].dead = false
            local ped = GetPlayerPed(match.team2[i].id)
            local respawnCoords
            if finished then
                respawnCoords = config.exitCoords
            else
                respawnCoords = match.mapdata.coords.team2
                -- Add slight offset for multiple players
                local offsetX = (i - 1) * 2.0
                local offsetY = (i - 1) * 1.0
                respawnCoords = {
                    x = respawnCoords.x + offsetX,
                    y = respawnCoords.y + offsetY,
                    z = respawnCoords.z,
                    w = respawnCoords.w
                }
            end
            SetEntityCoords(ped, respawnCoords.x, respawnCoords.y,
                respawnCoords.z, false, false, false, false)
            SetEntityHeading(ped, respawnCoords.w)
            if finished then
                SetPlayerRoutingBucket(match.team2[i].id, 0)
                Player(match.team2[i].id).state:set('inDuel', nil, true)
            end
            Wait(10)
        end
    end

    if not cause then
        cause = finished and 'finished_match' or 'next_round'
    end

    if cause == 'finished_match' or cause == 'timeout' then
        winner = match.team1points > match.team2points and 1 or match.team2points > match.team1points and 2 or 0
    end

    -- Store the end reason for match results
    match.endReason = cause

    triggerGameEvent(match, false, 'duels:nextMatchRound', match, finished, winner, cause)

    -- Log round end to Discord
    if not finished and logRoundEnd then
        logRoundEnd(match, winner)
    end

    if finished then
        -- Log match end to Discord
        if logMatchEnd then
            logMatchEnd(match, winner, cause)
        end
        finishMatch(match, winner, (cause == 'finished_match'))
    end
end

local function checkRoundFinished(match)
    local team1deads, team2deads = getMatchDeads(match)

    -- Handle different scenarios based on team sizes
    local roundFinished = false

    if #match.team1 == 0 then
        -- Team 1 is empty (custom mode) - team 2 wins automatically
        roundFinished = true
    elseif #match.team2 == 0 then
        -- Team 2 is empty (custom mode) - team 1 wins automatically
        roundFinished = true
    elseif team1deads == #match.team1 or team2deads == #match.team2 then
        -- Standard case - one team is completely eliminated
        roundFinished = true
    end

    if roundFinished then
        local winner = setRoundWinnerPoints(match)
        match.playingRound = match.playingRound + 1
        handleNextMatchRound(match, match.playingRound > match.rounds, winner)
    end
end

lib.callback.register('duels:checkAdmin', function(source)
    return isPlayerAdmin(source)
end)

lib.callback.register('duels:createLobby', function(playerId, data)
    Games[#Games + 1] = {
        id = generateDuelId(),
        created = os.time(),
        duelmode = data.duelmode,
        rounds = data.rounds,
        weapon = data.weapon,
        map = data.map,
        password = data.password,
        team1 = {
            {
                id = playerId,
                name = GetPlayerName(playerId),
                kills = 0,
                deaths = 0,
                host = true,
                dead = false
            }
        },
        team2 = {},
    }

    updateGlobalGames()

    return Games[#Games]
end)

lib.callback.register('duels:joinLobby', function(playerId, data)
    local lobbyId = data.lobbyId
    local lobbyType = data.type
    local lobbyPassword = data.password

    local game = getGameFromId(lobbyId)

    if not game then
        return false
    end

    if lobbyType ~= 'open' and lobbyPassword ~= game.password then
        bridge.notify(playerId, 'You entered a wrong password', 'error')
        return false
    end

    local totalplayers = tonumber(#game.team1 or 0) + tonumber(#game.team2 or 0)

    -- Check if it's custom mode (no player limits) or regular mode
    if game.duelmode ~= 'custom' then
        local maxPlayers = 10 -- Maximum total players allowed in a lobby for regular modes
        if totalplayers >= maxPlayers then
            bridge.notify(playerId, 'You cannot join the lobby because it is full (max 10 players)', 'error')
            return false
        end
    else
        -- Custom mode - only check server-wide limit
        local maxPlayersCustom = 20 -- Higher limit for custom mode
        if totalplayers >= maxPlayersCustom then
            bridge.notify(playerId, 'You cannot join the lobby because it is full (max 20 players in custom mode)', 'error')
            return false
        end
    end

    game.team1[#game.team1 + 1] = {
        id = playerId,
        name = GetPlayerName(playerId),
        kills = 0,
        deaths = 0,
        host = false,
        dead = false
    }

    updateGlobalGames()

    -- Log player join to Discord
    if logPlayerJoinLobby then
        logPlayerJoinLobby(GetPlayerName(playerId), playerId, lobbyId)
    end

    return true
end)

lib.callback.register('duels:joinTeam', function(playerId, data)
    local game = getGameFromId(data.lobbyId)
    if not game then
        return false
    end

    local success = switchTeam(playerId, game)

    if success then
        updateGlobalGames()
    end

    return success
end)

lib.callback.register('duels:leaveLobby', function(playerId, lobbyId)
    local game = getGameFromId(lobbyId)
    if not game then return false end

    if not isGamePlayer(game, playerId) then return false end

    if isGameHost(game, playerId) then
        for i = 1, #Games, 1 do
            if Games[i].id == lobbyId then
                table.remove(Games, i)
                break
            end
        end
        triggerGameEvent(game, true, 'duels:lobbyClosed')
    else
        for i = 1, #game.team1, 1 do
            if game.team1[i].id == playerId then
                table.remove(game.team1, i)
                break
            end
        end

        for i = 1, #game.team2, 1 do
            if game.team2[i].id == playerId then
                table.remove(game.team2, i)
                break
            end
        end
    end

    updateGlobalGames()

    -- Log player leave to Discord
    if logPlayerLeaveLobby then
        local reason = isGameHost(game, playerId) and 'Host left (lobby closed)' or 'Manual leave'
        logPlayerLeaveLobby(GetPlayerName(playerId), playerId, lobbyId, reason)
    end

    Wait(50)

    return true
end)

lib.callback.register('duels:startMatch', function(playerId, lobbyId, mapData)
    local game = getGameFromId(lobbyId)
    if not game then return false end

    if not isGameHost(game, playerId) then return false end

    local totalplayers = tonumber(#game.team1 or 0) + tonumber(#game.team2 or 0)

    if game.duelmode == 'custom' then
        -- Custom mode - very flexible rules
        local minPlayers = 1 -- Minimum 1 player total for custom mode

        if totalplayers < minPlayers then
            bridge.notify(playerId, 'You need at least 1 player to start a custom match', 'error')
            return false
        end

        -- Custom mode allows even single team matches (for practice/testing)
        -- No team balance requirements
    else
        -- Regular modes - standard rules
        local minPlayers = 2 -- Minimum 2 players total to start a match

        if totalplayers < minPlayers then
            bridge.notify(playerId, 'You need at least 2 players to start the match', 'error')
            return false
        end

        -- Ensure both teams have at least 1 player for regular modes
        if #game.team1 == 0 or #game.team2 == 0 then
            bridge.notify(playerId, 'Both teams need at least 1 player to start the match', 'error')
            return false
        end
    end

    StartedGames[#StartedGames + 1] = table.clone(game)

    local match = StartedGames[#StartedGames]

    match.team1points = 0
    match.team2points = 0
    match.playingRound = 1
    match.mapdata = mapData

    for i = 1, #Games, 1 do
        if Games[i].id == lobbyId then
            table.remove(Games, i)
            break
        end
    end

    updateGlobalGames()

    triggerGameEvent(match, false, 'duels:matchInitializing', match)

    Wait(500)

    SetRoutingBucketPopulationEnabled(match.id, false)

    -- Spawn team1 players (if any)
    if #match.team1 > 0 then
        for i = 1, #match.team1, 1 do
            SetPlayerRoutingBucket(match.team1[i].id, match.id)

            Wait(50)

            Player(match.team1[i].id).state:set('inDuel', match.id, true)

            local ped = GetPlayerPed(match.team1[i].id)
            local spawnCoords = mapData.coords.team1

            -- Add slight offset for multiple players to avoid overlap
            local offsetX = (i - 1) * 2.0 -- 2 units apart
            local offsetY = (i - 1) * 1.0 -- 1 unit apart

            SetEntityCoords(ped, spawnCoords.x + offsetX, spawnCoords.y + offsetY, spawnCoords.z, false, false, false, false)
            SetEntityHeading(ped, spawnCoords.w)
        end
    end

    -- Spawn team2 players (if any)
    if #match.team2 > 0 then
        for i = 1, #match.team2, 1 do
            SetPlayerRoutingBucket(match.team2[i].id, match.id)

            Wait(50)

            Player(match.team2[i].id).state:set('inDuel', match.id, true)

            local ped = GetPlayerPed(match.team2[i].id)
            local spawnCoords = mapData.coords.team2

            -- Add slight offset for multiple players to avoid overlap
            local offsetX = (i - 1) * 2.0 -- 2 units apart
            local offsetY = (i - 1) * 1.0 -- 1 unit apart

            SetEntityCoords(ped, spawnCoords.x + offsetX, spawnCoords.y + offsetY, spawnCoords.z, false, false, false, false)
            SetEntityHeading(ped, spawnCoords.w)
        end
    end

    Wait(500)

    triggerGameEvent(match, false, 'duels:matchInit')

    -- Log match start to Discord
    if logMatchStart then
        logMatchStart(match)
    end

    return true
end)

RegisterNetEvent('duels:someoneDied', function(killerServerId, killerClientId)
    local deadId = source
    local matchId = Player(deadId).state.inDuel
    local killerId = killerServerId

    if not matchId then return end

    local match = getMatchFromId(matchId)

    if not match then return end

    if not isGamePlayer(match, deadId) then return end

    matchAddPlayerDeath(match, deadId)

    local killerName, victimName = 'Unknown', GetPlayerName(deadId)

    if killerId and isGamePlayer(match, killerId) then
        matchAddPlayerKill(match, killerId)
        killerName = GetPlayerName(killerId)

        -- Log kill to Discord
        if logPlayerKill then
            logPlayerKill(match, killerName, victimName, match.weapon)
        end
    end

    checkRoundFinished(match)
end)

RegisterNetEvent('duels:matchTimerOut', function()
    local playerId = source

    local matchId = Player(playerId).state.inDuel
    if not matchId then return end

    local match = getMatchFromId(matchId)
    if not match then return end

    handleNextMatchRound(match, true, 0, 'timeout')
end)

RegisterNetEvent('duels:exitRequest', function()
    local playerId = source
    local matchId = Player(playerId).state.inDuel
    if not matchId then return end

    local match = getMatchFromId(matchId)
    if not match then return end
    
    handleNextMatchRound(match, true, 0, 'player_disconnect')
end)

-- handle player dropped
AddEventHandler('playerDropped', function(reason)
    local playerId = source
    local state = Player(playerId).state

    local matchId = state.inDuel
    if not matchId then return end

    local match = getMatchFromId(matchId)
    if not match then return end

    handleNextMatchRound(match, true, 0, 'player_disconnect')
end)

lib.callback.register('duels:adminCommand', function(source, data)
    if not isPlayerAdmin(source) then
        return { success = false, message = 'You do not have permission to use admin commands' }
    end

    local command = data.command
    local commandData = data.data or {}

    if command == 'listMatches' then
        local activeMatches = {}
        for i = 1, #Matches do
            local match = Matches[i]
            activeMatches[#activeMatches + 1] = {
                id = match.id,
                map = match.mapdata.label,
                weapon = match.weapon,
                players = #match.team1 + #match.team2,
                round = match.playingRound .. '/' .. match.rounds
            }
        end
        return { success = true, message = 'Active matches: ' .. json.encode(activeMatches) }

    elseif command == 'listLobbies' then
        local activeLobbies = {}
        for i = 1, #Games do
            local game = Games[i]
            if not game.started then
                activeLobbies[#activeLobbies + 1] = {
                    id = game.id,
                    mode = game.duelmode,
                    players = #game.team1 + #game.team2,
                    host = game.host.name
                }
            end
        end
        return { success = true, message = 'Active lobbies: ' .. json.encode(activeLobbies) }

    elseif command == 'endMatch' then
        local match = getMatchFromId(tonumber(commandData.matchId))
        if not match then
            return { success = false, message = 'Match not found' }
        end
        handleNextMatchRound(match, true, 0, 'admin_force_end')
        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Force End Match', commandData.matchId, 'Forced match end')
        end
        return { success = true, message = 'Match ended successfully' }

    elseif command == 'kickPlayer' then
        local targetPlayer = nil
        for i = 1, #Matches do
            local match = Matches[i]
            for j = 1, #match.team1 do
                if match.team1[j].name == commandData.playerName then
                    targetPlayer = match.team1[j].id
                    break
                end
            end
            if not targetPlayer then
                for j = 1, #match.team2 do
                    if match.team2[j].name == commandData.playerName then
                        targetPlayer = match.team2[j].id
                        break
                    end
                end
            end
            if targetPlayer then break end
        end

        if not targetPlayer then
            return { success = false, message = 'Player not found in any match' }
        end

        -- Remove player from match
        TriggerEvent('duels:playerDisconnected', targetPlayer)
        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Kick Player', commandData.playerName, 'Kicked from match')
        end
        return { success = true, message = 'Player kicked successfully' }

    elseif command == 'tpMatch' then
        local match = getMatchFromId(tonumber(commandData.matchId))
        if not match then
            return { success = false, message = 'Match not found' }
        end
        SetPlayerRoutingBucket(source, match.id)
        local ped = GetPlayerPed(source)
        local coords = match.mapdata.coords.team1
        SetEntityCoords(ped, coords.x, coords.y, coords.z, false, false, false, false)
        return { success = true, message = 'Teleported to match' }

    elseif command == 'tpBack' then
        SetPlayerRoutingBucket(source, 0)
        local ped = GetPlayerPed(source)
        local coords = config.exitCoords
        SetEntityCoords(ped, coords.x, coords.y, coords.z, false, false, false, false)
        return { success = true, message = 'Teleported back' }

    elseif command == 'closeLobby' then
        local game = getGameFromId(tonumber(commandData.lobbyId))
        if not game then
            return { success = false, message = 'Lobby not found' }
        end
        -- Remove lobby from Games array
        for i = #Games, 1, -1 do
            if Games[i].id == game.id then
                table.remove(Games, i)
                break
            end
        end
        updateGlobalGames()
        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Close Lobby', commandData.lobbyId, 'Forced lobby closure')
        end
        return { success = true, message = 'Lobby closed successfully' }

    elseif command == 'addWeapon' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.weapons then
            return { success = false, message = 'Failed to load client config' }
        end

        -- Check if weapon already exists
        for i = 1, #clientConfig.weapons do
            if clientConfig.weapons[i].value == commandData.weaponHash then
                return { success = false, message = 'Weapon already exists' }
            end
        end

        -- Add new weapon
        clientConfig.weapons[#clientConfig.weapons + 1] = {
            value = commandData.weaponHash,
            label = commandData.weaponLabel
        }

        -- Save to KVP
        local weaponsData = GetResourceKvpString('duels_weapons')
        local weapons = weaponsData and json.decode(weaponsData) or {}
        weapons[#weapons + 1] = {
            value = commandData.weaponHash,
            label = commandData.weaponLabel
        }
        SetResourceKvp('duels_weapons', json.encode(weapons))

        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Add Weapon', commandData.weaponHash, 'Added weapon: ' .. commandData.weaponLabel)
        end
        return { success = true, message = 'Weapon added successfully' }

    elseif command == 'removeWeapon' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.weapons then
            return { success = false, message = 'Failed to load client config' }
        end

        -- Find and remove weapon
        local found = false
        for i = #clientConfig.weapons, 1, -1 do
            if clientConfig.weapons[i].value == commandData.weaponHash then
                table.remove(clientConfig.weapons, i)
                found = true
                break
            end
        end

        if not found then
            return { success = false, message = 'Weapon not found' }
        end

        -- Update KVP
        local weaponsData = GetResourceKvpString('duels_weapons')
        local weapons = weaponsData and json.decode(weaponsData) or {}
        for i = #weapons, 1, -1 do
            if weapons[i].value == commandData.weaponHash then
                table.remove(weapons, i)
                break
            end
        end
        SetResourceKvp('duels_weapons', json.encode(weapons))

        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Remove Weapon', commandData.weaponHash, 'Removed weapon')
        end
        return { success = true, message = 'Weapon removed successfully' }

    elseif command == 'listWeapons' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.weapons then
            return { success = false, message = 'Failed to load client config' }
        end

        local weaponList = {}
        for i = 1, #clientConfig.weapons do
            weaponList[#weaponList + 1] = clientConfig.weapons[i].label .. ' (' .. clientConfig.weapons[i].value .. ')'
        end
        return { success = true, message = 'Weapons: ' .. table.concat(weaponList, ', ') }

    elseif command == 'addMap' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.maps then
            return { success = false, message = 'Failed to load client config' }
        end

        -- Check if map already exists
        for i = 1, #clientConfig.maps do
            if clientConfig.maps[i].value == commandData.mapValue then
                return { success = false, message = 'Map already exists' }
            end
        end

        -- Add new map
        local newMap = {
            value = commandData.mapValue,
            label = commandData.mapLabel,
            coords = {
                team1 = vector4(commandData.team1X, commandData.team1Y, commandData.team1Z, commandData.team1W),
                team2 = vector4(commandData.team2X, commandData.team2Y, commandData.team2Z, commandData.team2W)
            }
        }
        clientConfig.maps[#clientConfig.maps + 1] = newMap

        -- Save to KVP
        local mapsData = GetResourceKvpString('duels_maps')
        local maps = mapsData and json.decode(mapsData) or {}
        maps[#maps + 1] = {
            value = commandData.mapValue,
            label = commandData.mapLabel,
            coords = {
                team1 = {x = commandData.team1X, y = commandData.team1Y, z = commandData.team1Z, w = commandData.team1W},
                team2 = {x = commandData.team2X, y = commandData.team2Y, z = commandData.team2Z, w = commandData.team2W}
            }
        }
        SetResourceKvp('duels_maps', json.encode(maps))

        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Add Map', commandData.mapValue, 'Added map: ' .. commandData.mapLabel)
        end
        return { success = true, message = 'Map added successfully' }

    elseif command == 'removeMap' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.maps then
            return { success = false, message = 'Failed to load client config' }
        end

        -- Find and remove map
        local found = false
        for i = #clientConfig.maps, 1, -1 do
            if clientConfig.maps[i].value == commandData.mapValue then
                table.remove(clientConfig.maps, i)
                found = true
                break
            end
        end

        if not found then
            return { success = false, message = 'Map not found' }
        end

        -- Update KVP
        local mapsData = GetResourceKvpString('duels_maps')
        local maps = mapsData and json.decode(mapsData) or {}
        for i = #maps, 1, -1 do
            if maps[i].value == commandData.mapValue then
                table.remove(maps, i)
                break
            end
        end
        SetResourceKvp('duels_maps', json.encode(maps))

        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Remove Map', commandData.mapValue, 'Removed map')
        end
        return { success = true, message = 'Map removed successfully' }

    elseif command == 'listMaps' then
        local clientConfig = import('data')
        if not clientConfig or not clientConfig.maps then
            return { success = false, message = 'Failed to load client config' }
        end

        local mapList = {}
        for i = 1, #clientConfig.maps do
            mapList[#mapList + 1] = clientConfig.maps[i].label .. ' (' .. clientConfig.maps[i].value .. ')'
        end
        return { success = true, message = 'Maps: ' .. table.concat(mapList, ', ') }

    elseif command == 'getPos' then
        local ped = GetPlayerPed(source)
        local coords = GetEntityCoords(ped)
        local heading = GetEntityHeading(ped)
        local posString = string.format('Position: %.2f, %.2f, %.2f, %.2f', coords.x, coords.y, coords.z, heading)
        return { success = true, message = posString }

    elseif command == 'playerStats' then
        -- This would need to be implemented based on your stats system
        return { success = true, message = 'Player stats feature not yet implemented' }

    elseif command == 'clearStats' then
        -- This would need to be implemented based on your stats system
        if logAdminAction then
            logAdminAction(GetPlayerName(source), source, 'Clear Stats', 'ALL', 'Cleared all player statistics')
        end
        return { success = true, message = 'All player statistics cleared' }

    else
        return { success = false, message = 'Unknown command: ' .. command }
    end
end)

exports('isPlaying', function(source)
    return Player(source).state.inDuel ~= nil
end)
