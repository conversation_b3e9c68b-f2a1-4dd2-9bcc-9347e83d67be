import { Button, Card, Flex, Group, NumberInput, Select, Text, TextInput, Textarea, Title, Alert, Badge } from "@mantine/core";
import { useForm } from '@mantine/form';
import React, { FC, useState } from "react";
import { useGlobalStore } from "../states/useGlobalState";
import { fetchNui } from "../utils/fetchNui";

export const AdminPanel: FC = () => {
    const messages = useGlobalStore(state => state.messages);
    const [activeSection, setActiveSection] = useState<string>('matches');
    const [commandOutput, setCommandOutput] = useState<string>('');

    // Match Management Form
    const matchForm = useForm({
        initialValues: {
            matchId: '',
            playerName: '',
            lobbyId: ''
        }
    });

    // Weapon Management Form
    const weaponForm = useForm({
        initialValues: {
            weaponHash: '',
            weaponLabel: ''
        }
    });

    // Map Management Form
    const mapForm = useForm({
        initialValues: {
            mapValue: '',
            mapLabel: '',
            team1X: 0,
            team1Y: 0,
            team1Z: 0,
            team1W: 0,
            team2X: 0,
            team2Y: 0,
            team2Z: 0,
            team2W: 0
        }
    });

    const executeCommand = async (command: string, data: any = {}) => {
        try {
            const result = await fetchNui('adminCommand', { command, data });
            setCommandOutput(result?.message || 'Command executed successfully');
        } catch (error) {
            setCommandOutput('Error executing command: ' + error);
        }
    };

    const sections = [
        { id: 'matches', label: 'Match Management', icon: '🎮' },
        { id: 'weapons', label: 'Weapon Management', icon: '🔫' },
        { id: 'maps', label: 'Map Management', icon: '🗺️' },
        { id: 'players', label: 'Player Management', icon: '👥' },
        { id: 'system', label: 'System Tools', icon: '⚙️' }
    ];

    return (
        <Flex direction="column" gap="md">
            <Title order={3} c="white">🛡️ Admin Panel</Title>
            
            {/* Section Navigation */}
            <Group>
                {sections.map(section => (
                    <Button
                        key={section.id}
                        variant={activeSection === section.id ? "filled" : "outline"}
                        size="xs"
                        onClick={() => setActiveSection(section.id)}
                    >
                        {section.icon} {section.label}
                    </Button>
                ))}
            </Group>

            {/* Command Output */}
            {commandOutput && (
                <Alert color="blue" onClose={() => setCommandOutput('')} withCloseButton>
                    <Text size="sm">{commandOutput}</Text>
                </Alert>
            )}

            {/* Match Management Section */}
            {activeSection === 'matches' && (
                <Card>
                    <Title order={4} mb="md">🎮 Match Management</Title>
                    <Flex direction="column" gap="sm">
                        <Group>
                            <Button onClick={() => executeCommand('listMatches')} size="sm">
                                List Active Matches
                            </Button>
                            <Button onClick={() => executeCommand('listLobbies')} size="sm">
                                List Active Lobbies
                            </Button>
                        </Group>
                        
                        <TextInput
                            label="Match ID"
                            placeholder="Enter match ID"
                            {...matchForm.getInputProps('matchId')}
                        />
                        <Group>
                            <Button 
                                onClick={() => executeCommand('endMatch', { matchId: matchForm.values.matchId })}
                                color="red"
                                size="sm"
                                disabled={!matchForm.values.matchId}
                            >
                                End Match
                            </Button>
                            <Button 
                                onClick={() => executeCommand('tpMatch', { matchId: matchForm.values.matchId })}
                                size="sm"
                                disabled={!matchForm.values.matchId}
                            >
                                Teleport to Match
                            </Button>
                        </Group>

                        <TextInput
                            label="Player Name"
                            placeholder="Enter player name"
                            {...matchForm.getInputProps('playerName')}
                        />
                        <Button 
                            onClick={() => executeCommand('kickPlayer', { playerName: matchForm.values.playerName })}
                            color="orange"
                            size="sm"
                            disabled={!matchForm.values.playerName}
                        >
                            Kick Player from Match
                        </Button>

                        <TextInput
                            label="Lobby ID"
                            placeholder="Enter lobby ID"
                            {...matchForm.getInputProps('lobbyId')}
                        />
                        <Button 
                            onClick={() => executeCommand('closeLobby', { lobbyId: matchForm.values.lobbyId })}
                            color="red"
                            size="sm"
                            disabled={!matchForm.values.lobbyId}
                        >
                            Close Lobby
                        </Button>
                    </Flex>
                </Card>
            )}

            {/* Weapon Management Section */}
            {activeSection === 'weapons' && (
                <Card>
                    <Title order={4} mb="md">🔫 Weapon Management</Title>
                    <Flex direction="column" gap="sm">
                        <Button onClick={() => executeCommand('listWeapons')} size="sm" mb="md">
                            List All Weapons
                        </Button>
                        
                        <Title order={5}>Add New Weapon</Title>
                        <TextInput
                            label="Weapon Hash"
                            placeholder="e.g., WEAPON_PISTOL"
                            {...weaponForm.getInputProps('weaponHash')}
                        />
                        <TextInput
                            label="Weapon Label"
                            placeholder="e.g., Pistol"
                            {...weaponForm.getInputProps('weaponLabel')}
                        />
                        <Button 
                            onClick={() => executeCommand('addWeapon', { 
                                weaponHash: weaponForm.values.weaponHash,
                                weaponLabel: weaponForm.values.weaponLabel
                            })}
                            color="green"
                            size="sm"
                            disabled={!weaponForm.values.weaponHash || !weaponForm.values.weaponLabel}
                        >
                            Add Weapon
                        </Button>

                        <Title order={5} mt="md">Remove Weapon</Title>
                        <TextInput
                            label="Weapon Hash to Remove"
                            placeholder="e.g., WEAPON_PISTOL"
                            value={weaponForm.values.weaponHash}
                            onChange={(e) => weaponForm.setFieldValue('weaponHash', e.target.value)}
                        />
                        <Button 
                            onClick={() => executeCommand('removeWeapon', { weaponHash: weaponForm.values.weaponHash })}
                            color="red"
                            size="sm"
                            disabled={!weaponForm.values.weaponHash}
                        >
                            Remove Weapon
                        </Button>
                    </Flex>
                </Card>
            )}

            {/* Map Management Section */}
            {activeSection === 'maps' && (
                <Card>
                    <Title order={4} mb="md">🗺️ Map Management</Title>
                    <Flex direction="column" gap="sm">
                        <Group>
                            <Button onClick={() => executeCommand('listMaps')} size="sm">
                                List All Maps
                            </Button>
                            <Button onClick={() => executeCommand('getPos')} size="sm">
                                Get Current Position
                            </Button>
                        </Group>
                        
                        <Title order={5} mt="md">Add New Map</Title>
                        <Group grow>
                            <TextInput
                                label="Map Value"
                                placeholder="e.g., my_custom_map"
                                {...mapForm.getInputProps('mapValue')}
                            />
                            <TextInput
                                label="Map Label"
                                placeholder="e.g., My Custom Map"
                                {...mapForm.getInputProps('mapLabel')}
                            />
                        </Group>
                        
                        <Text size="sm" c="dimmed">Team 1 Spawn Coordinates</Text>
                        <Group grow>
                            <NumberInput
                                label="X"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team1X')}
                            />
                            <NumberInput
                                label="Y"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team1Y')}
                            />
                            <NumberInput
                                label="Z"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team1Z')}
                            />
                            <NumberInput
                                label="Heading"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team1W')}
                            />
                        </Group>
                        
                        <Text size="sm" c="dimmed">Team 2 Spawn Coordinates</Text>
                        <Group grow>
                            <NumberInput
                                label="X"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team2X')}
                            />
                            <NumberInput
                                label="Y"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team2Y')}
                            />
                            <NumberInput
                                label="Z"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team2Z')}
                            />
                            <NumberInput
                                label="Heading"
                                placeholder="0.0"
                                precision={2}
                                {...mapForm.getInputProps('team2W')}
                            />
                        </Group>
                        
                        <Button 
                            onClick={() => executeCommand('addMap', mapForm.values)}
                            color="green"
                            size="sm"
                            disabled={!mapForm.values.mapValue || !mapForm.values.mapLabel}
                        >
                            Add Map
                        </Button>

                        <Title order={5} mt="md">Remove Map</Title>
                        <TextInput
                            label="Map Value to Remove"
                            placeholder="e.g., my_custom_map"
                            value={mapForm.values.mapValue}
                            onChange={(e) => mapForm.setFieldValue('mapValue', e.target.value)}
                        />
                        <Button 
                            onClick={() => executeCommand('removeMap', { mapValue: mapForm.values.mapValue })}
                            color="red"
                            size="sm"
                            disabled={!mapForm.values.mapValue}
                        >
                            Remove Map
                        </Button>
                    </Flex>
                </Card>
            )}

            {/* Player Management Section */}
            {activeSection === 'players' && (
                <Card>
                    <Title order={4} mb="md">👥 Player Management</Title>
                    <Flex direction="column" gap="sm">
                        <TextInput
                            label="Player Name"
                            placeholder="Enter player name"
                            {...matchForm.getInputProps('playerName')}
                        />
                        <Button 
                            onClick={() => executeCommand('playerStats', { playerName: matchForm.values.playerName })}
                            size="sm"
                            disabled={!matchForm.values.playerName}
                        >
                            Get Player Statistics
                        </Button>
                        
                        <Alert color="red" mt="md">
                            <Text size="sm" weight={500}>⚠️ Danger Zone</Text>
                            <Text size="xs">This action cannot be undone!</Text>
                        </Alert>
                        <Button 
                            onClick={() => executeCommand('clearStats', { confirm: 'CONFIRM' })}
                            color="red"
                            size="sm"
                        >
                            Clear All Player Statistics
                        </Button>
                    </Flex>
                </Card>
            )}

            {/* System Tools Section */}
            {activeSection === 'system' && (
                <Card>
                    <Title order={4} mb="md">⚙️ System Tools</Title>
                    <Flex direction="column" gap="sm">
                        <Button onClick={() => executeCommand('tpBack')} size="sm">
                            Teleport Back from Match
                        </Button>
                        
                        <Alert color="blue" mt="md">
                            <Text size="sm">
                                💡 <strong>Tip:</strong> All admin actions are logged to Discord for audit purposes.
                            </Text>
                        </Alert>
                    </Flex>
                </Card>
            )}
        </Flex>
    );
};
