AddEventHandler('gameEventTriggered', function(event, data)
    if event ~= 'CEventNetworkEntityDamage' then return end
    if not LocalPlayer.state.inDuel then return end
    if isDead then return end  -- Prevent false triggers during your revive logic

    local victim = data[1]
    local victimDied = data[4]
    if not IsPedAPlayer(victim) or not victimDied then return end

    local playerPed = PlayerPedId()
    if victim ~= playerPed then return end
    if not (IsPedDeadOrDying(victim, true) or IsPedFatallyInjured(victim)) then return end

    Wait(math.random(250, 1250))

    -- Recheck in case revive already occurred
    if not IsPedDeadOrDying(playerPed, true) then return end

    local killerEntity = GetPedSourceOfDeath(playerPed)
    local killerClientId = NetworkGetPlayerIndexFromPed(killerEntity)

    if killerEntity ~= playerPed and killerClientId and NetworkIsPlayerActive(killerClientId) then
        TriggerServerEvent('duels:someoneDied', GetPlayerServerId(killerClientId), killerClientId)
    else
        TriggerServerEvent('duels:someoneDied')
    end
end)
