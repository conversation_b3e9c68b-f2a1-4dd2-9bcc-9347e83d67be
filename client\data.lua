return {
    matchTime = 30000, -- in seconds
    maxRounds = 100,   -- max playable rounds in a duel
    exitDuelCommand = 'exitduel', -- command to exit a duel when you are in one
    weapons = {      -- available weapons to play in duels
        { value = 'WEAPON_SIN_SCAR',     label = 'Scar' },
        { value = 'WEAPON_MK47',     label = 'MK47' },
        { value = 'WEAPON_PISTOL50', label = 'Deagle' },
        { value = 'WEAPON_APPISTOL', label = 'AP Pistol' },
        { value = 'WEAPON_COMBAT_PISTOL_CHROMIUM',  label = 'Chrome Combat Pistol' },
        { value = 'WEAPON_M9_P_CHROMIUM', label = 'M9 Chrome Pistol' },
        { value = 'WEAPON_OC_GDEAGLE', label = 'Golden Deagle' },
        { value = 'WEAPON_DOUBLEACTION', label = 'Golden Rev' },
        { value = 'WEAPON_SIN_DEAGLE', label = 'SinCity Deagle' },
        { value = 'WEAPON_OC_DEAGLE', label = 'OverCast Deagle' },
        { value = 'WEAPON_2TAP_DEAGLE', label = '2 Tap Deagle' },



    },
    maps = {
        {
            value = 'skyramps',
            label = 'Sky Ramps',
            coords = {
                team1 = vec4(-2943.06, -3171.18, 1349.17, 0.00),
                team2 = vec4(-2919.79, -3171.13, 1349.18, 0.00)
            }
        },
        {
            value = 'paint_ball_arena',
            label = 'Paint Ball Arena',
            coords = {
                team1 = vec4(2016.19, 2706.72, 49.96, 0.00),
                team2 = vec4(2027.81, 2858.16, 50.18, 0.0)
            }
        },
        {
            value = '1v1arena',
            label = '1v1 Arena',
            coords = {
                team1 = vec4(1127.63, 173.38, 258.46, 0.00),
                team2 = vec4(1153.86, 219.54, 258.49, 0.00)
            }
        },
        {
            value = 'Airport_Termi',
            label = 'Airport terminal',
            coords = {
                team1 = vec4(-1034.89, -2781.23, 4.64, 0.00),
                team2 = vec4(-1094.19, -2712.34, 0.81, 0.00)
            }
        },
        {
            value = 'cargo_ship',
            label = 'Cargo Ship',
            coords = {
                team1 = vec4(1240.66, -2882.95, 9.31, 0.00),
                team2 = vec4(1240.01, -3033.50, 9.32, 0.00)
            }
        },
        {
            value = 'pvp_arena3',
            label = 'PvP Arena 3',
            coords = {
                team1 = vec4(-216.41, -4348.75, 191.50, 0.00),
                team2 = vec4(-80.23, -4348.08, 191.50, 0.00)
            }
        },
        {
            value = 'Legion_bank',
            label = 'Legion Bank',
            coords = {
                team1 = vec4(145.13, -1063.08, 29.19, 0.00),
                team2 = vec4(144.72, -1040.43, 29.37, 0.00)
            }
        },
        {
            value = 'nuketown',
            label = 'Nuke Town',
            coords = {
                team1 = vec4(3422.09, -1026.20, 64.21, 0.00),
                team2 = vec4(3501.49, -1026.70, 64.21, 0.00)
            }
        },
        {
            value = 'stadiumbig',
            label = 'Stadium Big (5v5)',
            coords = {
                team1 = vec4(4353.09, -686.75, 233.78, 0.00),
                team2 = vec4(4341.11, -837.43, 233.78, 0.00)
            }
        },
        {
            value = 'stadiummid',
            label = 'Stadium Mid',
            coords = {
                team1 = vec4(4343.85, -1666.55, 233.78, 0.00),
                team2 = vec4(4344.25, -1601.02, 233.78, 0.00)
            }
        },
        {
            value = 'stadiumsmall',
            label = 'Stadium Small',
            coords = {
                team1 = vec4(4343.83, -1232.38, 233.78, 0.00),
                team2 = vec4(4343.72, -1161.39, 233.78, 0.00)
            }
        }
    }
}