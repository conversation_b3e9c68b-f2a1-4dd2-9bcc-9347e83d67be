local discordConfig = {
    enabled = true,
    webhooks = {
        matches = '', -- Discord webhook URL for match events
        admin = '',   -- Discord webhook URL for admin actions
        general = ''  -- Discord webhook URL for general events
    },
    colors = {
        match_start = 3066993,    -- Green
        match_end = 15158332,     -- Red
        player_join = 3447003,    -- Blue
        player_leave = 16776960,  -- Yellow
        admin_action = 16711680,  -- Bright Red
        kill = 16753920,          -- Orange
        round_end = 8359053       -- Purple
    }
}

local function sendDiscordLog(webhook, embed)
    if not discordConfig.enabled or not webhook or webhook == '' then
        return
    end

    local data = {
        embeds = { embed }
    }

    PerformHttpRequest(webhook, function(err, text, headers)
        if err ~= 200 then
            print('[DUELS] Discord webhook error: ' .. tostring(err))
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

local function formatPlayerList(players)
    local playerNames = {}
    for i = 1, #players do
        playerNames[i] = players[i].name .. ' (' .. players[i].id .. ')'
    end
    return table.concat(playerNames, '\n')
end

local function getServerName()
    return GetConvar('sv_hostname', 'FiveM Server')
end

local function getTimestamp()
    return os.date('%Y-%m-%dT%H:%M:%S')
end

-- Match Events
function logMatchStart(match)
    local embed = {
        title = '🎮 Match Started',
        description = 'A new duel match has begun!',
        color = discordConfig.colors.match_start,
        fields = {
            {
                name = 'Match ID',
                value = tostring(match.id),
                inline = true
            },
            {
                name = 'Map',
                value = match.mapdata.label or match.map,
                inline = true
            },
            {
                name = 'Weapon',
                value = match.weapon,
                inline = true
            },
            {
                name = 'Rounds',
                value = tostring(match.rounds),
                inline = true
            },
            {
                name = 'Mode',
                value = match.duelmode == 'custom' and '🔧 Custom Mode (Any Size)' or (match.duelmode or 'Unknown'),
                inline = true
            },
            {
                name = 'Team 1 (' .. #match.team1 .. ' players)',
                value = #match.team1 > 0 and formatPlayerList(match.team1) or 'Empty',
                inline = true
            },
            {
                name = 'Team 2 (' .. #match.team2 .. ' players)',
                value = #match.team2 > 0 and formatPlayerList(match.team2) or 'Empty',
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }
    
    sendDiscordLog(discordConfig.webhooks.matches, embed)
end

function logMatchEnd(match, winner, reason)
    local winnerText = 'Draw'
    if winner == 1 then
        winnerText = 'Team 1'
    elseif winner == 2 then
        winnerText = 'Team 2'
    end

    local reasonText = reason or 'finished_match'
    local reasonEmoji = '🏁'
    if reason == 'timeout' then
        reasonEmoji = '⏰'
    elseif reason == 'player_disconnect' then
        reasonEmoji = '🚪'
    end

    local embed = {
        title = reasonEmoji .. ' Match Ended',
        description = 'Match #' .. match.id .. ' has concluded',
        color = discordConfig.colors.match_end,
        fields = {
            {
                name = 'Winner',
                value = winnerText,
                inline = true
            },
            {
                name = 'Final Score',
                value = match.team1points .. ' - ' .. match.team2points,
                inline = true
            },
            {
                name = 'Reason',
                value = reasonText:gsub('_', ' '):upper(),
                inline = true
            },
            {
                name = 'Duration',
                value = 'Round ' .. (match.playingRound - 1) .. '/' .. match.rounds,
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    sendDiscordLog(discordConfig.webhooks.matches, embed)
end

function logPlayerKill(match, killerName, victimName, weapon)
    local embed = {
        title = '💀 Player Kill',
        description = killerName .. ' eliminated ' .. victimName,
        color = discordConfig.colors.kill,
        fields = {
            {
                name = 'Match ID',
                value = tostring(match.id),
                inline = true
            },
            {
                name = 'Weapon',
                value = weapon or 'Unknown',
                inline = true
            },
            {
                name = 'Round',
                value = tostring(match.playingRound),
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    sendDiscordLog(discordConfig.webhooks.matches, embed)
end

function logRoundEnd(match, winner)
    local winnerText = winner == 0 and 'Draw' or ('Team ' .. winner)
    
    local embed = {
        title = '🏆 Round Completed',
        description = 'Round ' .. match.playingRound .. ' finished',
        color = discordConfig.colors.round_end,
        fields = {
            {
                name = 'Match ID',
                value = tostring(match.id),
                inline = true
            },
            {
                name = 'Round Winner',
                value = winnerText,
                inline = true
            },
            {
                name = 'Current Score',
                value = match.team1points .. ' - ' .. match.team2points,
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    sendDiscordLog(discordConfig.webhooks.matches, embed)
end

-- Player Events
function logPlayerJoinLobby(playerName, playerId, lobbyId)
    local embed = {
        title = '👋 Player Joined Lobby',
        description = playerName .. ' joined a duel lobby',
        color = discordConfig.colors.player_join,
        fields = {
            {
                name = 'Player',
                value = playerName .. ' (' .. playerId .. ')',
                inline = true
            },
            {
                name = 'Lobby ID',
                value = tostring(lobbyId),
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    sendDiscordLog(discordConfig.webhooks.general, embed)
end

function logPlayerLeaveLobby(playerName, playerId, lobbyId, reason)
    local embed = {
        title = '🚪 Player Left Lobby',
        description = playerName .. ' left a duel lobby',
        color = discordConfig.colors.player_leave,
        fields = {
            {
                name = 'Player',
                value = playerName .. ' (' .. playerId .. ')',
                inline = true
            },
            {
                name = 'Lobby ID',
                value = tostring(lobbyId),
                inline = true
            },
            {
                name = 'Reason',
                value = reason or 'Manual leave',
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    sendDiscordLog(discordConfig.webhooks.general, embed)
end

-- Admin Events
function logAdminAction(adminName, adminId, action, target, details)
    local embed = {
        title = '🛡️ Admin Action',
        description = 'Administrative action performed',
        color = discordConfig.colors.admin_action,
        fields = {
            {
                name = 'Admin',
                value = adminName .. ' (' .. adminId .. ')',
                inline = true
            },
            {
                name = 'Action',
                value = action,
                inline = true
            },
            {
                name = 'Target',
                value = target or 'N/A',
                inline = true
            }
        },
        footer = {
            text = getServerName()
        },
        timestamp = getTimestamp()
    }

    if details then
        table.insert(embed.fields, {
            name = 'Details',
            value = details,
            inline = false
        })
    end

    sendDiscordLog(discordConfig.webhooks.admin, embed)
end

-- Configuration function
function setDiscordWebhooks(webhooks)
    if webhooks.matches then discordConfig.webhooks.matches = webhooks.matches end
    if webhooks.admin then discordConfig.webhooks.admin = webhooks.admin end
    if webhooks.general then discordConfig.webhooks.general = webhooks.general end
end

function setDiscordEnabled(enabled)
    discordConfig.enabled = enabled
end

-- Export functions for use in other files
_G.logMatchStart = logMatchStart
_G.logMatchEnd = logMatchEnd
_G.logPlayerKill = logPlayerKill
_G.logRoundEnd = logRoundEnd
_G.logPlayerJoinLobby = logPlayerJoinLobby
_G.logPlayerLeaveLobby = logPlayerLeaveLobby
_G.logAdminAction = logAdminAction
_G.setDiscordWebhooks = setDiscordWebhooks
_G.setDiscordEnabled = setDiscordEnabled
