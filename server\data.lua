config = {
    mainIdenfitier = 'discord', -- the identifier that you use to store user data (steam, license, discord, ...)
    exitCoords = vec4(-540.32, -212.49, 37.65, 0.00),
    clearStatsOnRestart = false, -- if true, the stats will be cleared when the server/script restarts

    -- Discord Logging Configuration
    discord = {
        enabled = true, -- Set to false to disable Discord logging
        webhooks = {
            matches = '', -- Discord webhook URL for match events (match start/end, kills, rounds)
            admin = '',   -- Discord webhook URL for admin actions
            general = ''  -- Discord webhook URL for general events (player join/leave)
        }
    },

    -- Admin Configuration
    admins = {
        -- Add admin identifiers here (discord, steam, license, etc.)
        -- Example: 'discord:123456789012345678'
        'discord:846599992449302549'
    }
}