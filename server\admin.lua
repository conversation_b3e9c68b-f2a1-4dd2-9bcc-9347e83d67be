-- Admin Tools System for Yisus Duels

local function isPlayerAdmin(playerId)
    if not config.admins or #config.admins == 0 then
        return false
    end
    
    local identifiers = GetPlayerIdentifiers(playerId)
    for _, identifier in pairs(identifiers) do
        for _, adminId in pairs(config.admins) do
            if identifier == adminId then
                return true
            end
        end
    end
    return false
end

local function getPlayerFromPartialName(partialName)
    local players = GetPlayers()
    local matches = {}
    
    for _, playerId in pairs(players) do
        local playerName = GetPlayerName(playerId)
        if playerName and string.find(playerName:lower(), partialName:lower()) then
            table.insert(matches, {id = tonumber(playerId), name = playerName})
        end
    end
    
    return matches
end

-- Admin Commands

-- Force end a match
RegisterCommand('duels_endmatch', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local matchId = tonumber(args[1])
    if not matchId then
        bridge.notify(source, 'Usage: /duels_endmatch <match_id>', 'error')
        return
    end
    
    local match = getMatchFromId(matchId)
    if not match then
        bridge.notify(source, 'Match not found', 'error')
        return
    end
    
    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Force End Match', 'Match #' .. matchId, 'Match forcefully ended by admin')
    end
    
    handleNextMatchRound(match, true, 0, 'admin_force_end')
    bridge.notify(source, 'Match #' .. matchId .. ' has been forcefully ended', 'success')
end, true)

-- Kick player from match
RegisterCommand('duels_kickplayer', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetName = args[1]
    if not targetName then
        bridge.notify(source, 'Usage: /duels_kickplayer <player_name>', 'error')
        return
    end
    
    local matches = getPlayerFromPartialName(targetName)
    if #matches == 0 then
        bridge.notify(source, 'Player not found', 'error')
        return
    elseif #matches > 1 then
        bridge.notify(source, 'Multiple players found, be more specific', 'error')
        return
    end
    
    local targetPlayer = matches[1]
    local matchId = Player(targetPlayer.id).state.inDuel
    
    if not matchId then
        bridge.notify(source, targetPlayer.name .. ' is not in a duel', 'error')
        return
    end
    
    local match = getMatchFromId(matchId)
    if not match then
        bridge.notify(source, 'Match not found', 'error')
        return
    end
    
    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Kick Player from Match', targetPlayer.name, 'Player kicked from match #' .. matchId)
    end
    
    -- Remove player from match and end it
    handleNextMatchRound(match, true, 0, 'admin_kick')
    bridge.notify(source, targetPlayer.name .. ' has been kicked from the match', 'success')
    bridge.notify(targetPlayer.id, 'You have been kicked from the match by an admin', 'error')
end, true)

-- List active matches
RegisterCommand('duels_listmatches', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    if #StartedGames == 0 then
        bridge.notify(source, 'No active matches', 'info')
        return
    end
    
    local matchList = 'Active Matches:\n'
    for i = 1, #StartedGames do
        local match = StartedGames[i]
        local team1Count = #match.team1
        local team2Count = #match.team2
        matchList = matchList .. string.format('Match #%d: %dv%d on %s (Round %d/%d)\n', 
            match.id, team1Count, team2Count, match.map, match.playingRound, match.rounds)
    end
    
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested match list')
    print(matchList)
    bridge.notify(source, 'Match list printed to server console', 'info')
end, true)

-- List active lobbies
RegisterCommand('duels_listlobbies', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    if #Games == 0 then
        bridge.notify(source, 'No active lobbies', 'info')
        return
    end
    
    local lobbyList = 'Active Lobbies:\n'
    for i = 1, #Games do
        local lobby = Games[i]
        local team1Count = #lobby.team1
        local team2Count = #lobby.team2
        local hostName = 'Unknown'
        
        -- Find host name
        for j = 1, #lobby.team1 do
            if lobby.team1[j].host then
                hostName = lobby.team1[j].name
                break
            end
        end
        
        lobbyList = lobbyList .. string.format('Lobby #%d: %dv%d %s, Host: %s\n', 
            lobby.id, team1Count, team2Count, lobby.duelmode, hostName)
    end
    
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested lobby list')
    print(lobbyList)
    bridge.notify(source, 'Lobby list printed to server console', 'info')
end, true)

-- Close a lobby
RegisterCommand('duels_closelobby', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local lobbyId = tonumber(args[1])
    if not lobbyId then
        bridge.notify(source, 'Usage: /duels_closelobby <lobby_id>', 'error')
        return
    end
    
    local lobby = getGameFromId(lobbyId)
    if not lobby then
        bridge.notify(source, 'Lobby not found', 'error')
        return
    end
    
    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Force Close Lobby', 'Lobby #' .. lobbyId, 'Lobby forcefully closed by admin')
    end
    
    -- Remove lobby and notify players
    for i = 1, #Games do
        if Games[i].id == lobbyId then
            triggerGameEvent(Games[i], false, 'duels:lobbyClosed')
            table.remove(Games, i)
            break
        end
    end
    
    updateGlobalGames()
    bridge.notify(source, 'Lobby #' .. lobbyId .. ' has been closed', 'success')
end, true)

-- Teleport to match
RegisterCommand('duels_tpmatch', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local matchId = tonumber(args[1])
    if not matchId then
        bridge.notify(source, 'Usage: /duels_tpmatch <match_id>', 'error')
        return
    end
    
    local match = getMatchFromId(matchId)
    if not match then
        bridge.notify(source, 'Match not found', 'error')
        return
    end
    
    -- Teleport admin to match location
    local ped = GetPlayerPed(source)
    local coords = match.mapdata.coords.player[1] -- Use first spawn point
    
    SetEntityCoords(ped, coords.x, coords.y, coords.z + 10.0, false, false, false, false) -- +10 to spawn above
    SetPlayerRoutingBucket(source, matchId) -- Put in same routing bucket
    
    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Teleport to Match', 'Match #' .. matchId, 'Admin teleported to spectate match')
    end
    
    bridge.notify(source, 'Teleported to match #' .. matchId, 'success')
end, true)

-- Return from match spectating
RegisterCommand('duels_tpback', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local ped = GetPlayerPed(source)
    SetEntityCoords(ped, config.exitCoords.x, config.exitCoords.y, config.exitCoords.z, false, false, false, false)
    SetPlayerRoutingBucket(source, 0) -- Return to main world
    
    bridge.notify(source, 'Returned to main world', 'success')
end, true)

-- Clear all stats (dangerous command)
RegisterCommand('duels_clearstats', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local confirm = args[1]
    if confirm ~= 'CONFIRM' then
        bridge.notify(source, 'This will clear ALL player statistics! Use: /duels_clearstats CONFIRM', 'error')
        return
    end
    
    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Clear All Statistics', 'ALL PLAYERS', 'All player statistics cleared')
    end
    
    SetResourceKvp('leaderboard', json.encode({}))
    bridge.notify(source, 'All player statistics have been cleared', 'success')
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' cleared all player statistics')
end, true)

-- Get player duel stats
RegisterCommand('duels_playerstats', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetName = args[1]
    if not targetName then
        bridge.notify(source, 'Usage: /duels_playerstats <player_name>', 'error')
        return
    end
    
    local matches = getPlayerFromPartialName(targetName)
    if #matches == 0 then
        bridge.notify(source, 'Player not found', 'error')
        return
    elseif #matches > 1 then
        bridge.notify(source, 'Multiple players found, be more specific', 'error')
        return
    end
    
    local targetPlayer = matches[1]
    local identifier = getIdentifier(targetPlayer.id, config.mainIdenfitier)
    
    -- Get stats from leaderboard
    local lbStr = GetResourceKvpString('leaderboard')
    local lb = lbStr and json.decode(lbStr) or {}
    local playerStats = lb[identifier]
    
    if not playerStats then
        bridge.notify(source, targetPlayer.name .. ' has no duel statistics', 'info')
        return
    end
    
    local statsMsg = string.format('%s Stats: K/D: %d/%d (%.2f), W/L: %d/%d (%.2f)', 
        targetPlayer.name, 
        playerStats.kills, 
        playerStats.deaths, 
        playerStats.deaths > 0 and (playerStats.kills / playerStats.deaths) or playerStats.kills,
        playerStats.wins,
        playerStats.loses,
        playerStats.loses > 0 and (playerStats.wins / playerStats.loses) or playerStats.wins
    )
    
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested stats for ' .. targetPlayer.name)
    print(statsMsg)
    bridge.notify(source, 'Player stats printed to console', 'info')
end, true)

-- Weapon Management Commands

-- Add a new weapon
RegisterCommand('duels_addweapon', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local weaponHash = args[1]
    local weaponLabel = table.concat(args, ' ', 2)

    if not weaponHash or not weaponLabel then
        bridge.notify(source, 'Usage: /duels_addweapon <weapon_hash> <weapon_label>', 'error')
        return
    end

    -- Get current weapons from client data
    local clientConfig = import('data')
    if not clientConfig or not clientConfig.weapons then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    -- Check if weapon already exists
    for i = 1, #clientConfig.weapons do
        if clientConfig.weapons[i].value == weaponHash then
            bridge.notify(source, 'Weapon already exists: ' .. weaponHash, 'error')
            return
        end
    end

    -- Add new weapon
    clientConfig.weapons[#clientConfig.weapons + 1] = {
        value = weaponHash,
        label = weaponLabel
    }

    -- Save to KVP for persistence
    local weaponsData = GetResourceKvpString('duels_weapons')
    local weapons = weaponsData and json.decode(weaponsData) or {}
    weapons[#weapons + 1] = {
        value = weaponHash,
        label = weaponLabel
    }
    SetResourceKvp('duels_weapons', json.encode(weapons))

    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Add Weapon', weaponHash, 'Added weapon: ' .. weaponLabel)
    end

    bridge.notify(source, 'Weapon added: ' .. weaponLabel .. ' (' .. weaponHash .. ')', 'success')
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' added weapon: ' .. weaponLabel)
end, true)

-- Remove a weapon
RegisterCommand('duels_removeweapon', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local weaponHash = args[1]
    if not weaponHash then
        bridge.notify(source, 'Usage: /duels_removeweapon <weapon_hash>', 'error')
        return
    end

    -- Get current weapons from client data
    local clientConfig = import('data')
    if not clientConfig or not clientConfig.weapons then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    -- Find and remove weapon
    local found = false
    local removedLabel = ''
    for i = #clientConfig.weapons, 1, -1 do
        if clientConfig.weapons[i].value == weaponHash then
            removedLabel = clientConfig.weapons[i].label
            table.remove(clientConfig.weapons, i)
            found = true
            break
        end
    end

    if not found then
        bridge.notify(source, 'Weapon not found: ' .. weaponHash, 'error')
        return
    end

    -- Update KVP
    local weaponsData = GetResourceKvpString('duels_weapons')
    local weapons = weaponsData and json.decode(weaponsData) or {}
    for i = #weapons, 1, -1 do
        if weapons[i].value == weaponHash then
            table.remove(weapons, i)
            break
        end
    end
    SetResourceKvp('duels_weapons', json.encode(weapons))

    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Remove Weapon', weaponHash, 'Removed weapon: ' .. removedLabel)
    end

    bridge.notify(source, 'Weapon removed: ' .. removedLabel .. ' (' .. weaponHash .. ')', 'success')
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' removed weapon: ' .. removedLabel)
end, true)

-- List all weapons
RegisterCommand('duels_listweapons', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local clientConfig = import('data')
    if not clientConfig or not clientConfig.weapons then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    if #clientConfig.weapons == 0 then
        bridge.notify(source, 'No weapons configured', 'info')
        return
    end

    local weaponList = 'Available Weapons:\n'
    for i = 1, #clientConfig.weapons do
        local weapon = clientConfig.weapons[i]
        weaponList = weaponList .. string.format('%d. %s (%s)\n', i, weapon.label, weapon.value)
    end

    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested weapon list')
    print(weaponList)
    bridge.notify(source, 'Weapon list printed to server console', 'info')
end, true)

-- Map Management Commands

-- Add a new map
RegisterCommand('duels_addmap', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local mapValue = args[1]
    local mapLabel = args[2]
    local team1X = tonumber(args[3])
    local team1Y = tonumber(args[4])
    local team1Z = tonumber(args[5])
    local team1W = tonumber(args[6])
    local team2X = tonumber(args[7])
    local team2Y = tonumber(args[8])
    local team2Z = tonumber(args[9])
    local team2W = tonumber(args[10])

    if not mapValue or not mapLabel or not team1X or not team1Y or not team1Z or not team1W or
       not team2X or not team2Y or not team2Z or not team2W then
        bridge.notify(source, 'Usage: /duels_addmap <value> <label> <t1_x> <t1_y> <t1_z> <t1_w> <t2_x> <t2_y> <t2_z> <t2_w>', 'error')
        return
    end

    -- Get current maps from client data
    local clientConfig = import('data')
    if not clientConfig or not clientConfig.maps then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    -- Check if map already exists
    for i = 1, #clientConfig.maps do
        if clientConfig.maps[i].value == mapValue then
            bridge.notify(source, 'Map already exists: ' .. mapValue, 'error')
            return
        end
    end

    -- Add new map
    local newMap = {
        value = mapValue,
        label = mapLabel,
        coords = {
            team1 = vector4(team1X, team1Y, team1Z, team1W),
            team2 = vector4(team2X, team2Y, team2Z, team2W)
        }
    }

    clientConfig.maps[#clientConfig.maps + 1] = newMap

    -- Save to KVP for persistence
    local mapsData = GetResourceKvpString('duels_maps')
    local maps = mapsData and json.decode(mapsData) or {}
    maps[#maps + 1] = {
        value = mapValue,
        label = mapLabel,
        coords = {
            team1 = {x = team1X, y = team1Y, z = team1Z, w = team1W},
            team2 = {x = team2X, y = team2Y, z = team2Z, w = team2W}
        }
    }
    SetResourceKvp('duels_maps', json.encode(maps))

    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Add Map', mapValue, 'Added map: ' .. mapLabel)
    end

    bridge.notify(source, 'Map added: ' .. mapLabel .. ' (' .. mapValue .. ')', 'success')
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' added map: ' .. mapLabel)
end, true)

-- Remove a map
RegisterCommand('duels_removemap', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local mapValue = args[1]
    if not mapValue then
        bridge.notify(source, 'Usage: /duels_removemap <map_value>', 'error')
        return
    end

    -- Get current maps from client data
    local clientConfig = import('data')
    if not clientConfig or not clientConfig.maps then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    -- Find and remove map
    local found = false
    local removedLabel = ''
    for i = #clientConfig.maps, 1, -1 do
        if clientConfig.maps[i].value == mapValue then
            removedLabel = clientConfig.maps[i].label
            table.remove(clientConfig.maps, i)
            found = true
            break
        end
    end

    if not found then
        bridge.notify(source, 'Map not found: ' .. mapValue, 'error')
        return
    end

    -- Update KVP
    local mapsData = GetResourceKvpString('duels_maps')
    local maps = mapsData and json.decode(mapsData) or {}
    for i = #maps, 1, -1 do
        if maps[i].value == mapValue then
            table.remove(maps, i)
            break
        end
    end
    SetResourceKvp('duels_maps', json.encode(maps))

    -- Log admin action
    if logAdminAction then
        logAdminAction(GetPlayerName(source), source, 'Remove Map', mapValue, 'Removed map: ' .. removedLabel)
    end

    bridge.notify(source, 'Map removed: ' .. removedLabel .. ' (' .. mapValue .. ')', 'success')
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' removed map: ' .. removedLabel)
end, true)

-- List all maps
RegisterCommand('duels_listmaps', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local clientConfig = import('data')
    if not clientConfig or not clientConfig.maps then
        bridge.notify(source, 'Failed to load client config', 'error')
        return
    end

    if #clientConfig.maps == 0 then
        bridge.notify(source, 'No maps configured', 'info')
        return
    end

    local mapList = 'Available Maps:\n'
    for i = 1, #clientConfig.maps do
        local map = clientConfig.maps[i]
        mapList = mapList .. string.format('%d. %s (%s)\n', i, map.label, map.value)
        mapList = mapList .. string.format('   Team1: %.2f, %.2f, %.2f, %.2f\n',
            map.coords.team1.x, map.coords.team1.y, map.coords.team1.z, map.coords.team1.w)
        mapList = mapList .. string.format('   Team2: %.2f, %.2f, %.2f, %.2f\n',
            map.coords.team2.x, map.coords.team2.y, map.coords.team2.z, map.coords.team2.w)
    end

    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested map list')
    print(mapList)
    bridge.notify(source, 'Map list printed to server console', 'info')
end, true)

-- Get current position for map creation
RegisterCommand('duels_getpos', function(source, args, rawCommand)
    if not isPlayerAdmin(source) then
        bridge.notify(source, 'You do not have permission to use this command', 'error')
        return
    end

    local ped = GetPlayerPed(source)
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)

    local posString = string.format('Current Position: %.2f, %.2f, %.2f, %.2f', coords.x, coords.y, coords.z, heading)
    print('[DUELS ADMIN] ' .. GetPlayerName(source) .. ' requested position: ' .. posString)
    bridge.notify(source, 'Position printed to server console', 'info')
end, true)

print('[DUELS] Admin tools loaded. Available commands:')
print('  /duels_endmatch <match_id> - Force end a match')
print('  /duels_kickplayer <player_name> - Kick player from match')
print('  /duels_listmatches - List active matches')
print('  /duels_listlobbies - List active lobbies')
print('  /duels_closelobby <lobby_id> - Close a lobby')
print('  /duels_tpmatch <match_id> - Teleport to match for spectating')
print('  /duels_tpback - Return from match spectating')
print('  /duels_clearstats CONFIRM - Clear all player statistics')
print('  /duels_playerstats <player_name> - Get player statistics')
print('  /duels_addweapon <hash> <label> - Add a new weapon')
print('  /duels_removeweapon <hash> - Remove a weapon')
print('  /duels_listweapons - List all available weapons')
print('  /duels_addmap <value> <label> <t1_x> <t1_y> <t1_z> <t1_w> <t2_x> <t2_y> <t2_z> <t2_w> - Add a new map')
print('  /duels_removemap <value> - Remove a map')
print('  /duels_listmaps - List all available maps')
print('  /duels_getpos - Get current position for map creation')
